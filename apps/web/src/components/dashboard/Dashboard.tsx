import { <PERSON><PERSON> } from "@/components/ui/button";
import {
    <PERSON>,
    CardContent,
    CardDescription,
    <PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON>,
    Card<PERSON><PERSON><PERSON>,
} from "@/components/ui/card";
import { Ta<PERSON>, <PERSON><PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { useAuth } from "@/hooks/useAuth";
import { useLocations } from "@/hooks/useLocations";
import {
    Area<PERSON>hart,
    Bar<PERSON>hart,
    Clock,
    FileText,
    LayoutDashboard,
    Plus,
    Users,
} from "lucide-react";
import { useState } from "react";
import { useNavigate } from "react-router-dom";

// Import dashboard components
import { RecentActivity } from "./activity/RecentActivity";
import { AppointmentMetrics } from "./analytics/AppointmentMetrics";
import { PatientDemographics } from "./analytics/PatientDemographics";
import { UpcomingAppointments } from "./appointments/UpcomingAppointments";
import { DashboardBreadcrumb } from "./DashboardBreadcrumb";
import { RecentPatients } from "./patients/RecentPatients";
import { StatsOverview } from "./stats/StatsOverview";
import { TasksList } from "./tasks/TasksList";

export function Dashboard() {
  const { user } = useAuth();
  const { selectedLocation } = useLocations();
  const [activeTab, setActiveTab] = useState("overview");
  const navigate = useNavigate();

  return (
    <div className="w-full max-w-full">
      <DashboardBreadcrumb />
      <div className="flex flex-col sm:flex-row sm:items-center justify-between mb-6 gap-4">
        <div>
          <h1 className="text-2xl font-bold tracking-tight">Dashboard</h1>
          <p className="text-muted-foreground">
            Welcome back, {user?.user_metadata?.first_name || "User"}
            {selectedLocation && (
              <span className="ml-2 text-primary font-medium">
                • {selectedLocation.name}
              </span>
            )}
          </p>
        </div>
        <div className="flex items-center gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => navigate("/activity")}
          >
            <Clock className="mr-2 h-4 w-4" />
            <span>Activity Log</span>
          </Button>
          <Button size="sm" onClick={() => navigate("/patients/new")}>
            <Plus className="mr-2 h-4 w-4" />
            <span>New Patient</span>
          </Button>
        </div>
      </div>

      {/* Stats Overview */}
      <StatsOverview />

      {/* Dashboard Tabs */}
      <Tabs
        defaultValue="overview"
        value={activeTab}
        onValueChange={setActiveTab}
        className="mb-6"
      >
        <TabsList className="grid grid-cols-2 sm:grid-cols-4 w-full max-w-[600px]">
          <TabsTrigger value="overview">
            <LayoutDashboard className="h-4 w-4 mr-2" />
            <span className="hidden sm:inline">Overview</span>
            <span className="sm:hidden">Home</span>
          </TabsTrigger>
          <TabsTrigger value="patients">
            <Users className="h-4 w-4 mr-2" />
            Patients
          </TabsTrigger>
          <TabsTrigger value="analytics">
            <BarChart className="h-4 w-4 mr-2" />
            <span className="hidden sm:inline">Analytics</span>
            <span className="sm:hidden">Stats</span>
          </TabsTrigger>
          <TabsTrigger value="reports">
            <FileText className="h-4 w-4 mr-2" />
            Reports
          </TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-4 mt-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Upcoming Appointments */}
            <UpcomingAppointments />

            {/* Tasks */}
            <TasksList />
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Recent Patients */}
            <RecentPatients />

            {/* Recent Activity */}
            <RecentActivity />
          </div>
        </TabsContent>

        <TabsContent value="patients" className="space-y-4 mt-4">
          <Card className="shadow-sm">
            <CardHeader className="pb-3">
              <div className="flex items-center justify-between">
                <CardTitle className="text-lg font-medium">
                  Patient Management
                </CardTitle>
                <Button
                  variant="ghost"
                  size="sm"
                  className="h-8 text-xs"
                  onClick={() => navigate("/patients")}
                >
                  View All Patients
                </Button>
              </div>
              <CardDescription>View and manage your patients</CardDescription>
            </CardHeader>
            <CardContent className="p-6 flex items-center justify-center">
              <div className="flex flex-col items-center justify-center gap-4">
                <Users className="h-16 w-16 text-muted-foreground" />
                <p className="text-muted-foreground text-center">
                  Access the full patient management system to view and manage
                  all patient records.
                </p>
                <Button variant="default" onClick={() => navigate("/patients")}>
                  Go to Patient Management
                </Button>
              </div>
            </CardContent>
            <CardFooter className="border-t p-4 bg-muted/50">
              <Button
                variant="outline"
                size="sm"
                className="w-full"
                onClick={() => navigate("/patients/new")}
              >
                <Plus className="mr-2 h-4 w-4" />
                Add New Patient
              </Button>
            </CardFooter>
          </Card>
        </TabsContent>

        <TabsContent value="analytics" className="space-y-4 mt-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Patient Demographics */}
            <PatientDemographics />

            {/* Appointment Metrics */}
            <AppointmentMetrics />
          </div>

          <Card className="shadow-sm">
            <CardHeader>
              <CardTitle className="text-lg font-medium">
                Appointment Trends
              </CardTitle>
              <CardDescription>Last 30 days</CardDescription>
            </CardHeader>
            <CardContent className="h-80 flex items-center justify-center">
              <div className="flex items-center justify-center w-full h-full">
                <AreaChart className="h-16 w-16 text-muted-foreground" />
                <p className="text-muted-foreground ml-4">
                  Advanced chart visualizations will be implemented in a future
                  update.
                </p>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="reports" className="space-y-4 mt-4">
          <Card className="shadow-sm">
            <CardHeader>
              <CardTitle className="text-lg font-medium">
                Available Reports
              </CardTitle>
              <CardDescription>Generate and download reports</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex items-center justify-between p-4 border rounded-md">
                  <div className="flex items-center gap-4">
                    <FileText className="h-5 w-5 text-primary" />
                    <div>
                      <p className="font-medium">Patient Visit Summary</p>
                      <p className="text-sm text-muted-foreground">
                        Summary of all patient visits
                      </p>
                    </div>
                  </div>
                  <Button size="sm">Generate</Button>
                </div>

                <div className="flex items-center justify-between p-4 border rounded-md">
                  <div className="flex items-center gap-4">
                    <FileText className="h-5 w-5 text-primary" />
                    <div>
                      <p className="font-medium">Financial Summary</p>
                      <p className="text-sm text-muted-foreground">
                        Overview of financial performance
                      </p>
                    </div>
                  </div>
                  <Button size="sm">Generate</Button>
                </div>

                <div className="flex items-center justify-between p-4 border rounded-md">
                  <div className="flex items-center gap-4">
                    <FileText className="h-5 w-5 text-primary" />
                    <div>
                      <p className="font-medium">Staff Performance</p>
                      <p className="text-sm text-muted-foreground">
                        Productivity and performance metrics
                      </p>
                    </div>
                  </div>
                  <Button size="sm">Generate</Button>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
