import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { useAuth } from "@/hooks/useAuth";
import { useUserRoles } from "@/hooks/useUserRoles";
import { PatientFilters as PatientFiltersType } from "@/types/patient";
import { Search, X } from "lucide-react";
import { useCallback, useEffect, useState } from "react";

interface PatientFiltersProps {
  filters: PatientFiltersType;
  onFiltersChange: (filters: PatientFiltersType) => void;
  totalCount?: number;
  className?: string;
}

export function PatientFilters({
  filters,
  onFiltersChange,
  totalCount = 0,
  className = "",
}: PatientFiltersProps) {
  const { organization } = useAuth();
  const { isSystemAdmin } = useUserRoles();
  const [organizations, setOrganizations] = useState<
    Array<{ id: string; name: string }>
  >([]);

  // Fetch organizations for system admin filter
  useEffect(() => {
    if (isSystemAdmin && organization?.id === "system-admin-no-org") {
      // Fetch organizations for the dropdown
      // This would be implemented similar to the organizations page
      // For now, we'll leave it empty
      setOrganizations([]);
    }
  }, [isSystemAdmin, organization]);

  const updateFilter = useCallback(
    (key: keyof PatientFiltersType, value: string | undefined) => {
      onFiltersChange({
        ...filters,
        [key]: value,
      });
    },
    [filters, onFiltersChange],
  );

  const clearFilters = useCallback(() => {
    onFiltersChange({
      search: "",
      status: "all",
      gender: "all",
      organizationId: undefined,
      ageRange: undefined,
      hasRecentVisit: undefined,
      hasUpcomingAppointment: undefined,
    });
  }, [onFiltersChange]);

  const hasActiveFilters =
    filters.search ||
    (filters.status && filters.status !== "all") ||
    (filters.gender && filters.gender !== "all") ||
    filters.organizationId ||
    filters.ageRange ||
    filters.hasRecentVisit ||
    filters.hasUpcomingAppointment;

  return (
    <div className={`space-y-4 ${className}`}>
      {/* Search and primary filters */}
      <div className="flex flex-col sm:flex-row gap-4">
        {/* Search */}
        <div className="relative flex-1">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
          <Input
            placeholder="Search patients by name or email..."
            value={filters.search}
            onChange={(e) => updateFilter("search", e.target.value)}
            className="pl-10"
          />
        </div>

        {/* Status filter */}
        <Select
          value={filters.status || "all"}
          onValueChange={(value) =>
            updateFilter("status", value === "all" ? undefined : value)
          }
        >
          <SelectTrigger className="w-full sm:w-[180px]">
            <SelectValue placeholder="Status" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Status</SelectItem>
            <SelectItem value="new">New</SelectItem>
            <SelectItem value="active">Active</SelectItem>
            <SelectItem value="inactive">Inactive</SelectItem>
            <SelectItem value="archived">Archived</SelectItem>
          </SelectContent>
        </Select>

        {/* Gender filter */}
        <Select
          value={filters.gender || "all"}
          onValueChange={(value) =>
            updateFilter("gender", value === "all" ? undefined : value)
          }
        >
          <SelectTrigger className="w-full sm:w-[180px]">
            <SelectValue placeholder="Gender" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Genders</SelectItem>
            <SelectItem value="male">Male</SelectItem>
            <SelectItem value="female">Female</SelectItem>
            <SelectItem value="other">Other</SelectItem>
            <SelectItem value="prefer_not_to_say">Prefer not to say</SelectItem>
          </SelectContent>
        </Select>

        {/* Organization filter (for system admins) */}
        {isSystemAdmin && organization?.id === "system-admin-no-org" && (
          <Select
            value={filters.organizationId || "all"}
            onValueChange={(value) =>
              updateFilter(
                "organizationId",
                value === "all" ? undefined : value,
              )
            }
          >
            <SelectTrigger className="w-full sm:w-[200px]">
              <SelectValue placeholder="Organization" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Organizations</SelectItem>
              {organizations.map((org) => (
                <SelectItem key={org.id} value={org.id}>
                  {org.name}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        )}
      </div>

      {/* Results summary and clear filters */}
      <div className="flex items-center justify-between">
        <div className="text-sm text-muted-foreground">
          {totalCount > 0 ? (
            <>
              Showing {totalCount} patient{totalCount !== 1 ? "s" : ""}
            </>
          ) : (
            "No patients found"
          )}
        </div>

        {hasActiveFilters && (
          <Button
            variant="ghost"
            size="sm"
            onClick={clearFilters}
            className="h-8 px-2 lg:px-3"
          >
            <X className="mr-2 h-4 w-4" />
            Clear filters
          </Button>
        )}
      </div>
    </div>
  );
}
