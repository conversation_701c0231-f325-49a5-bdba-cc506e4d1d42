import { Ava<PERSON>, AvatarFallback } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { useAuth } from "@/hooks/useAuth";
import { useUserRoles } from "@/hooks/useUserRoles";
import { PatientWithStats } from "@/types/patient";
import { Calendar, Edit, Eye, Mail, Phone } from "lucide-react";
import { useNavigate } from "react-router-dom";

interface PatientTableProps {
  patients: PatientWithStats[];
  isLoading?: boolean;
  onPatientClick?: (patient: PatientWithStats) => void;
  className?: string;
}

export function PatientTable({
  patients,
  isLoading,
  onPatientClick,
  className = "",
}: PatientTableProps) {
  const navigate = useNavigate();
  const { organization } = useAuth();
  const { isSystemAdmin } = useUserRoles();

  const getInitials = (firstName: string, lastName: string) => {
    return `${firstName.charAt(0)}${lastName.charAt(0)}`.toUpperCase();
  };

  const getStatusVariant = (status: string) => {
    switch (status) {
      case "new":
        return "bg-blue-100 text-blue-800 hover:bg-blue-200";
      case "active":
        return "bg-green-100 text-green-800 hover:bg-green-200";
      case "inactive":
        return "bg-yellow-100 text-yellow-800 hover:bg-yellow-200";
      case "archived":
        return "bg-gray-100 text-gray-800 hover:bg-gray-200";
      default:
        return "bg-gray-100 text-gray-800 hover:bg-gray-200";
    }
  };

  const formatDate = (dateString: string | null | undefined) => {
    if (!dateString) return "N/A";
    return new Date(dateString).toLocaleDateString();
  };

  const handlePatientClick = (patient: PatientWithStats) => {
    if (onPatientClick) {
      onPatientClick(patient);
    } else {
      navigate(`/patients/${patient.id}`);
    }
  };

  const handleEditClick = (e: React.MouseEvent, patient: PatientWithStats) => {
    e.stopPropagation();
    navigate(`/patients/${patient.id}/edit`);
  };

  const handleViewClick = (e: React.MouseEvent, patient: PatientWithStats) => {
    e.stopPropagation();
    navigate(`/patients/${patient.id}`);
  };

  if (isLoading) {
    return (
      <div className={`border rounded-lg ${className}`}>
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Patient</TableHead>
              <TableHead>Age</TableHead>
              <TableHead>Contact</TableHead>
              <TableHead>Status</TableHead>
              <TableHead>Last Visit</TableHead>
              <TableHead>Next Appointment</TableHead>
              {isSystemAdmin && organization?.id === "system-admin-no-org" && (
                <TableHead>Organization</TableHead>
              )}
              <TableHead className="text-right">Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {Array.from({ length: 5 }).map((_, i) => (
              <TableRow key={i}>
                <TableCell>
                  <div className="flex items-center gap-3">
                    <div className="h-10 w-10 bg-muted animate-pulse rounded-full" />
                    <div>
                      <div className="h-4 bg-muted animate-pulse rounded w-32 mb-1" />
                      <div className="h-3 bg-muted animate-pulse rounded w-20" />
                    </div>
                  </div>
                </TableCell>
                <TableCell>
                  <div className="h-4 bg-muted animate-pulse rounded w-8" />
                </TableCell>
                <TableCell>
                  <div className="h-4 bg-muted animate-pulse rounded w-24" />
                </TableCell>
                <TableCell>
                  <div className="h-6 bg-muted animate-pulse rounded w-16" />
                </TableCell>
                <TableCell>
                  <div className="h-4 bg-muted animate-pulse rounded w-20" />
                </TableCell>
                <TableCell>
                  <div className="h-4 bg-muted animate-pulse rounded w-20" />
                </TableCell>
                {isSystemAdmin &&
                  organization?.id === "system-admin-no-org" && (
                    <TableCell>
                      <div className="h-4 bg-muted animate-pulse rounded w-24" />
                    </TableCell>
                  )}
                <TableCell className="text-right">
                  <div className="flex justify-end gap-2">
                    <div className="h-8 w-8 bg-muted animate-pulse rounded" />
                    <div className="h-8 w-8 bg-muted animate-pulse rounded" />
                  </div>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </div>
    );
  }

  if (patients.length === 0) {
    return (
      <div className={`border rounded-lg p-12 text-center ${className}`}>
        <div className="text-muted-foreground">
          <Calendar className="mx-auto h-12 w-12 mb-4" />
          <h3 className="text-lg font-medium mb-2">No patients found</h3>
          <p>Try adjusting your search criteria or add a new patient.</p>
        </div>
      </div>
    );
  }

  return (
    <div className={`border rounded-lg ${className}`}>
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead>Patient</TableHead>
            <TableHead>Age</TableHead>
            <TableHead>Contact</TableHead>
            <TableHead>Status</TableHead>
            <TableHead>Last Visit</TableHead>
            <TableHead>Next Appointment</TableHead>
            {isSystemAdmin && organization?.id === "system-admin-no-org" && (
              <TableHead>Organization</TableHead>
            )}
            <TableHead className="text-right">Actions</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {patients.map((patient) => (
            <TableRow
              key={patient.id}
              className="cursor-pointer hover:bg-muted/50"
              onClick={() => handlePatientClick(patient)}
            >
              <TableCell>
                <div className="flex items-center gap-3">
                  <Avatar>
                    <AvatarFallback className="bg-primary/10 text-primary">
                      {getInitials(patient.first_name, patient.last_name)}
                    </AvatarFallback>
                  </Avatar>
                  <div>
                    <div className="font-medium">{patient.full_name}</div>
                    <div className="text-sm text-muted-foreground capitalize">
                      {patient.gender}
                    </div>
                  </div>
                </div>
              </TableCell>
              <TableCell>{patient.age}</TableCell>
              <TableCell>
                <div className="space-y-1">
                  {patient.email && (
                    <div className="flex items-center gap-1 text-sm">
                      <Mail className="h-3 w-3" />
                      <span className="truncate max-w-[150px]">
                        {patient.email}
                      </span>
                    </div>
                  )}
                  {patient.phone && (
                    <div className="flex items-center gap-1 text-sm">
                      <Phone className="h-3 w-3" />
                      <span>{patient.phone}</span>
                    </div>
                  )}
                </div>
              </TableCell>
              <TableCell>
                <Badge
                  variant="outline"
                  className={getStatusVariant(patient.status)}
                >
                  {patient.status}
                </Badge>
              </TableCell>
              <TableCell className="text-sm">
                {formatDate(patient.last_visit)}
              </TableCell>
              <TableCell className="text-sm">
                {formatDate(patient.next_appointment)}
              </TableCell>
              {isSystemAdmin && organization?.id === "system-admin-no-org" && (
                <TableCell className="text-sm">
                  {patient.organization?.name || "N/A"}
                </TableCell>
              )}
              <TableCell className="text-right">
                <div className="flex justify-end gap-2">
                  <Button
                    variant="ghost"
                    size="icon"
                    className="h-8 w-8"
                    onClick={(e) => handleViewClick(e, patient)}
                  >
                    <Eye className="h-4 w-4" />
                  </Button>
                  <Button
                    variant="ghost"
                    size="icon"
                    className="h-8 w-8"
                    onClick={(e) => handleEditClick(e, patient)}
                  >
                    <Edit className="h-4 w-4" />
                  </Button>
                </div>
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </div>
  );
}
