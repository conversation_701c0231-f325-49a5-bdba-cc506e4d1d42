import { useAuth } from "@/hooks/useAuth";
import { useLocations } from "@/hooks/useLocations";
import { supabase } from "@/lib/supabase";
import { ALL_LOCATIONS_ID } from "@/types/location";
import { useEffect, useState } from "react";
import { AppointmentMetrics, DashboardStats, PatientDemographics } from "./useAnalytics";

/**
 * Location-aware analytics hook that extends organization analytics
 * with location-specific filtering when a location is selected
 */
export function useLocationAnalytics() {
  const { organization } = useAuth();
  const { selectedLocation, shouldShowLocationSelector } = useLocations();
  
  const [stats, setStats] = useState<DashboardStats>({
    todayAppointments: 0,
    todayAppointmentsChange: 0,
    newPatients: 0,
    newPatientsChange: 0,
    totalProcedures: 0,
    totalProceduresChange: 0,
    revenue: 0,
    revenueChange: 0,
  });
  
  const [patientDemographics, setPatientDemographics] = useState<PatientDemographics>({
    ageGroups: [],
    genderDistribution: [],
  });
  
  const [appointmentMetrics, setAppointmentMetrics] = useState<AppointmentMetrics>({
    byStatus: [],
    byDepartment: [],
  });
  
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<Error | null>(null);

  // Determine if we should filter by location
  const shouldFilterByLocation = shouldShowLocationSelector && 
    selectedLocation && 
    selectedLocation.id !== ALL_LOCATIONS_ID;

  useEffect(() => {
    const fetchLocationAnalytics = async () => {
      if (!organization) {
        setIsLoading(false);
        return;
      }

      setIsLoading(true);
      setError(null);

      try {
        // Base date calculations
        const today = new Date();
        today.setHours(0, 0, 0, 0);
        const tomorrow = new Date(today);
        tomorrow.setDate(tomorrow.getDate() + 1);
        const yesterday = new Date(today);
        yesterday.setDate(yesterday.getDate() - 1);

        // Build appointment queries with location awareness
        let todayAppointmentsQuery = supabase
          .from("appointments")
          .select("id, department_id, departments!inner(location_id)", { count: "exact" })
          .gte("appointment_date", today.toISOString())
          .lt("appointment_date", tomorrow.toISOString())
          .eq("organization_id", organization.id);

        let yesterdayAppointmentsQuery = supabase
          .from("appointments")
          .select("id, department_id, departments!inner(location_id)", { count: "exact" })
          .gte("appointment_date", yesterday.toISOString())
          .lt("appointment_date", today.toISOString())
          .eq("organization_id", organization.id);

        // Apply location filter if needed
        if (shouldFilterByLocation) {
          todayAppointmentsQuery = todayAppointmentsQuery
            .eq("departments.location_id", selectedLocation.id);
          yesterdayAppointmentsQuery = yesterdayAppointmentsQuery
            .eq("departments.location_id", selectedLocation.id);
        }

        // Execute appointment queries
        const [todayResult, yesterdayResult] = await Promise.all([
          todayAppointmentsQuery,
          yesterdayAppointmentsQuery
        ]);

        if (todayResult.error) throw new Error(todayResult.error.message);
        if (yesterdayResult.error) throw new Error(yesterdayResult.error.message);

        const todayCount = todayResult.count || 0;
        const yesterdayCount = yesterdayResult.count || 0;
        const appointmentsChange = yesterdayCount > 0 
          ? Math.round(((todayCount - yesterdayCount) / yesterdayCount) * 100)
          : 0;

        // Build patient queries - patients are organization-level only for now
        const lastWeek = new Date();
        lastWeek.setDate(lastWeek.getDate() - 7);
        const twoWeeksAgo = new Date(lastWeek);
        twoWeeksAgo.setDate(twoWeeksAgo.getDate() - 7);

        const newPatientsQuery = supabase
          .from("patients")
          .select("id", { count: "exact" })
          .gte("created_at", lastWeek.toISOString())
          .eq("organization_id", organization.id);

        const previousWeekPatientsQuery = supabase
          .from("patients")
          .select("id", { count: "exact" })
          .gte("created_at", twoWeeksAgo.toISOString())
          .lt("created_at", lastWeek.toISOString())
          .eq("organization_id", organization.id);

        // Execute patient queries
        const [newPatientsResult, previousWeekResult] = await Promise.all([
          newPatientsQuery,
          previousWeekPatientsQuery
        ]);

        if (newPatientsResult.error) throw new Error(newPatientsResult.error.message);
        if (previousWeekResult.error) throw new Error(previousWeekResult.error.message);

        const newPatientsCount = newPatientsResult.count || 0;
        const previousWeekCount = previousWeekResult.count || 0;
        const patientsChange = previousWeekCount > 0 
          ? Math.round(((newPatientsCount - previousWeekCount) / previousWeekCount) * 100)
          : 0;

        // For now, use mock data for procedures and revenue
        const totalProcedures = 132;
        const totalProceduresChange = 3;
        const revenue = 15750;
        const revenueChange = 8;

        // Set the dashboard stats
        setStats({
          todayAppointments: todayCount,
          todayAppointmentsChange: appointmentsChange,
          newPatients: newPatientsCount,
          newPatientsChange: patientsChange,
          totalProcedures,
          totalProceduresChange,
          revenue,
          revenueChange,
        });

        // Fetch patient demographics - organization level
        const demographicsQuery = supabase
          .from("patients")
          .select("date_of_birth, gender")
          .eq("organization_id", organization.id);

        const { data: patients, error: demographicsError } = await demographicsQuery;

        if (demographicsError) throw new Error(demographicsError.message);

        // Process demographics data
        const ageGroups = {
          "0-17": 0,
          "18-34": 0,
          "35-50": 0,
          "51-65": 0,
          "65+": 0,
        };

        const genderCount = {
          male: 0,
          female: 0,
          other: 0,
        };

        patients?.forEach((patient) => {
          // Calculate age
          if (patient.date_of_birth) {
            const birthDate = new Date(patient.date_of_birth);
            const ageDiffMs = Date.now() - birthDate.getTime();
            const ageDate = new Date(ageDiffMs);
            const age = Math.abs(ageDate.getUTCFullYear() - 1970);

            if (age < 18) ageGroups["0-17"]++;
            else if (age < 35) ageGroups["18-34"]++;
            else if (age < 51) ageGroups["35-50"]++;
            else if (age < 66) ageGroups["51-65"]++;
            else ageGroups["65+"]++;
          }

          // Count gender
          const gender = patient.gender?.toLowerCase();
          if (gender === 'male') genderCount.male++;
          else if (gender === 'female') genderCount.female++;
          else genderCount.other++;
        });

        setPatientDemographics({
          ageGroups: Object.entries(ageGroups).map(([label, value]) => ({ label, value })),
          genderDistribution: Object.entries(genderCount).map(([label, value]) => ({ 
            label: label.charAt(0).toUpperCase() + label.slice(1), 
            value 
          })),
        });

        // Fetch appointment metrics with location awareness
        let appointmentMetricsQuery = supabase
          .from("appointments")
          .select("status, department_id, departments!inner(name, location_id)")
          .eq("organization_id", organization.id);

        if (shouldFilterByLocation) {
          appointmentMetricsQuery = appointmentMetricsQuery
            .eq("departments.location_id", selectedLocation.id);
        }

        const { data: appointmentData, error: appointmentError } = await appointmentMetricsQuery;

        if (appointmentError) throw new Error(appointmentError.message);

        // Process appointment metrics
        const statusCount: Record<string, number> = {};
        const departmentCount: Record<string, number> = {};

        appointmentData?.forEach((appointment) => {
          // Count by status
          const status = appointment.status || 'unknown';
          statusCount[status] = (statusCount[status] || 0) + 1;

          // Count by department
          const departments = appointment.departments as { name: string } | null;
          const deptName = departments?.name || 'Unknown';
          departmentCount[deptName] = (departmentCount[deptName] || 0) + 1;
        });

        setAppointmentMetrics({
          byStatus: Object.entries(statusCount).map(([label, value]) => ({ label, value })),
          byDepartment: Object.entries(departmentCount).map(([label, value]) => ({ label, value })),
        });

      } catch (err) {
        console.error('Error fetching location analytics:', err);
        setError(err instanceof Error ? err : new Error('Unknown error'));
      } finally {
        setIsLoading(false);
      }
    };

    fetchLocationAnalytics();
  }, [organization, selectedLocation, shouldFilterByLocation]);

  return {
    stats,
    patientDemographics,
    appointmentMetrics,
    isLoading,
    error,
    // Additional context information
    locationContext: {
      isFiltered: shouldFilterByLocation,
      locationName: selectedLocation?.name || null,
      locationId: selectedLocation?.id || null,
    }
  };
}


 