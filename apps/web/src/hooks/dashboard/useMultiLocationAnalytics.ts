import { useAuth } from "@/hooks/useAuth";
import { useLocations } from "@/hooks/useLocations";
import { supabase } from "@/lib/supabase";
import { ALL_LOCATIONS_ID } from "@/types/location";
import { useEffect, useState } from "react";

export interface LocationAnalytics {
  locationId: string;
  locationName: string;
  todayAppointments: number;
  totalPatients: number;
  revenue: number;
  utilizationRate: number;
}

/**
 * Multi-location analytics hook that aggregates data across all user-accessible locations
 * when "All Locations" is selected. This provides true location-aware aggregation
 * rather than simple organization-wide filtering.
 */
export function useMultiLocationAnalytics() {
  const { locations: userLocations, selectedLocation, shouldShowLocationSelector } = useLocations();
  const { organization } = useAuth();
  const [locationAnalytics, setLocationAnalytics] = useState<LocationAnalytics[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<Error | null>(null);

  // Determine if we should aggregate across multiple locations
  const shouldUseMultiLocationAggregation = shouldShowLocationSelector && 
    selectedLocation?.id === ALL_LOCATIONS_ID && 
    userLocations.length > 1;

  useEffect(() => {
    const fetchMultiLocationAnalytics = async () => {
      if (!organization || !shouldUseMultiLocationAggregation) {
        setLocationAnalytics([]);
        setIsLoading(false);
        return;
      }

      setIsLoading(true);
      setError(null);

      try {
        // Get today's date range
        const today = new Date();
        today.setHours(0, 0, 0, 0);
        const tomorrow = new Date(today);
        tomorrow.setDate(tomorrow.getDate() + 1);

        // Fetch analytics for each location
        const locationAnalyticsPromises = userLocations.map(async (location) => {
          // Skip the virtual "All Locations" entry if it exists
          if (location.id === ALL_LOCATIONS_ID) {
            return null;
          }

          try {
            // Fetch today's appointments for this location
            const { data: appointments, error: appointmentsError } = await supabase
              .from("appointments")
              .select("id, departments!inner(location_id)")
              .eq("departments.location_id", location.id)
              .gte("appointment_date", today.toISOString())
              .lt("appointment_date", tomorrow.toISOString())
              .eq("organization_id", organization.id);

            if (appointmentsError) {
              console.error(`Error fetching appointments for location ${location.id}:`, appointmentsError);
              return null;
            }

            // Fetch total patients for this location (approximation via appointments)
            const { data: patientAppointments, error: patientsError } = await supabase
              .from("appointments")
              .select("patient_id, departments!inner(location_id)")
              .eq("departments.location_id", location.id)
              .eq("organization_id", organization.id);

            if (patientsError) {
              console.error(`Error fetching patients for location ${location.id}:`, patientsError);
              return null;
            }

            // Count unique patients
            const uniquePatients = new Set(patientAppointments?.map(apt => apt.patient_id) || []);

            // Mock revenue and utilization data (replace with real calculations)
            const mockRevenue = Math.floor(Math.random() * 10000) + 5000;
            const mockUtilization = Math.floor(Math.random() * 40) + 60; // 60-100%

            return {
              locationId: location.id,
              locationName: location.name,
              todayAppointments: appointments?.length || 0,
              totalPatients: uniquePatients.size,
              revenue: mockRevenue,
              utilizationRate: mockUtilization,
            };
          } catch (error) {
            console.error(`Error processing analytics for location ${location.id}:`, error);
            return null;
          }
        });

        const results = await Promise.all(locationAnalyticsPromises);
        const validResults = results.filter((result): result is LocationAnalytics => result !== null);
        
        setLocationAnalytics(validResults);
      } catch (err) {
        console.error('Error fetching multi-location analytics:', err);
        setError(err instanceof Error ? err : new Error('Unknown error'));
      } finally {
        setIsLoading(false);
      }
    };

    fetchMultiLocationAnalytics();
  }, [organization, shouldUseMultiLocationAggregation, userLocations]);

  return {
    locationAnalytics,
    isLoading,
    error,
    isMultiLocationView: shouldUseMultiLocationAggregation,
  };
} 