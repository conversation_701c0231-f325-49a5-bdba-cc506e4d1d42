import { useAuth } from "@/hooks/useAuth";
import { useLocations } from "@/hooks/useLocations";
import { supabase } from "@/lib/supabase";
import { ALL_LOCATIONS_ID } from "@/types/location";
import { useCallback, useEffect, useState } from "react";

export interface Patient {
  id: string;
  first_name: string;
  last_name: string;
  email: string | null;
  phone: string | null;
  date_of_birth: string;
  gender: "male" | "female" | "other" | "prefer_not_to_say";
  address: string | null;
  emergency_contact: string | null;
  insurance_info: unknown | null;
  medical_history: unknown | null;
  created_at: string | null;
  updated_at: string | null;
  organization_id: string | null;
  user_id: string | null;
  search_vector: unknown | null;
  // Computed fields from joins
  appointment_count?: number;
  last_appointment_date?: string | null;
  next_appointment_date?: string | null;
  primary_department?: string | null;
  location_id?: string | null;
  appointments?: Array<{
    id: string;
    appointment_date: string;
    status: string;
    department: {
      id: string;
      name: string;
      location_id?: string | null;
    } | null;
  }>;
}

export interface PatientsFilters {
  search?: string;
  gender?: "male" | "female" | "other" | "prefer_not_to_say";
  ageRange?: [number, number];
  hasUpcomingAppointments?: boolean;
  department?: string;
  dateRange?: [Date, Date];
  limit?: number;
  recentOnly?: boolean;
}

export function usePatients(filters: PatientsFilters = {}) {
  const { organization } = useAuth();
  const { selectedLocation, shouldShowLocationSelector } = useLocations();
  
  const [patients, setPatients] = useState<Patient[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<Error | null>(null);
  const [totalCount, setTotalCount] = useState(0);

  // Determine if we should filter by location
  const shouldFilterByLocation = shouldShowLocationSelector &&
    selectedLocation &&
    selectedLocation.id !== ALL_LOCATIONS_ID;

  const fetchPatients = useCallback(async () => {
    if (!organization) {
      setPatients([]);
      setIsLoading(false);
      return;
    }

    setIsLoading(true);
    setError(null);

    try {
      // Start building the query with location-aware joins if needed
      let query = shouldFilterByLocation
        ? supabase
            .from("patients")
            .select(`
              *,
              appointments!left(
                id,
                appointment_date,
                status,
                department:departments!inner(id, name, location_id)
              )
            `, { count: "exact" })
        : supabase
            .from("patients")
            .select(`
              *,
              appointments!left(
                id,
                appointment_date,
                status,
                department:departments(id, name)
              )
            `, { count: "exact" });

      // Apply organization filter
      query = query.eq("organization_id", organization.id);

      // Apply location filter if needed
      if (shouldFilterByLocation) {
        query = query.eq('appointments.department.location_id', selectedLocation.id);
      }

      // Apply search filter
      if (filters.search) {
        const searchTerm = `%${filters.search}%`;
        query = query.or(
          `first_name.ilike.${searchTerm},last_name.ilike.${searchTerm},email.ilike.${searchTerm},phone.ilike.${searchTerm}`
        );
      }

      // Apply gender filter
      if (filters.gender) {
        query = query.eq("gender", filters.gender);
      }

      // Apply age range filter
      if (filters.ageRange) {
        const [minAge, maxAge] = filters.ageRange;
        const maxDate = new Date();
        maxDate.setFullYear(maxDate.getFullYear() - minAge);
        const minDate = new Date();
        minDate.setFullYear(minDate.getFullYear() - maxAge - 1);
        
        query = query
          .gte("date_of_birth", minDate.toISOString().split('T')[0])
          .lte("date_of_birth", maxDate.toISOString().split('T')[0]);
      }

      // Apply date range filter (for patient creation date)
      if (filters.dateRange) {
        const [startDate, endDate] = filters.dateRange;
        query = query
          .gte("created_at", startDate.toISOString())
          .lte("created_at", endDate.toISOString());
      }

      // Apply limit if specified
      if (filters.limit) {
        query = query.limit(filters.limit);
      }

      // Order by most recent first
      query = query.order("created_at", { ascending: false });

      const { data, error: fetchError, count } = await query;

      if (fetchError) {
        throw new Error(fetchError.message);
      }

      // Process the data to compute additional fields
      const processedPatients: Patient[] = (data || []).map((patient) => {
        const appointments = patient.appointments || [];
        const now = new Date();
        
        // Find upcoming and past appointments
        const upcomingAppointments = appointments.filter(
          (apt) => new Date(apt.appointment_date) > now && apt.status !== 'cancelled'
        );
        const pastAppointments = appointments.filter(
          (apt) => new Date(apt.appointment_date) <= now
        );

        // Get the most recent department
        const recentAppointment = appointments
          .sort((a, b) => new Date(b.appointment_date).getTime() - new Date(a.appointment_date).getTime())[0];

        return {
          ...patient,
          appointment_count: appointments.length,
          last_appointment_date: pastAppointments.length > 0 
            ? pastAppointments.sort((a: any, b: any) => 
                new Date(b.appointment_date).getTime() - new Date(a.appointment_date).getTime()
              )[0].appointment_date
            : null,
          next_appointment_date: upcomingAppointments.length > 0
            ? upcomingAppointments.sort((a: any, b: any) => 
                new Date(a.appointment_date).getTime() - new Date(b.appointment_date).getTime()
              )[0].appointment_date
            : null,
          primary_department: recentAppointment?.department?.name || null,
          location_id: (recentAppointment?.department as { id: string; name: string; location_id?: string | null })?.location_id || null,
        };
      });

      // Apply has upcoming appointments filter (post-processing)
      const filteredPatients = filters.hasUpcomingAppointments !== undefined
        ? processedPatients.filter(patient => 
            filters.hasUpcomingAppointments 
              ? patient.next_appointment_date !== null
              : patient.next_appointment_date === null
          )
        : processedPatients;

      // Apply department filter (post-processing)
      const finalPatients = filters.department
        ? filteredPatients.filter(patient => patient.primary_department === filters.department)
        : filteredPatients;

      setPatients(finalPatients);
      setTotalCount(count || 0);
    } catch (err) {
      console.error("Error fetching patients:", err);
      setError(err instanceof Error ? err : new Error("Failed to fetch patients"));
    } finally {
      setIsLoading(false);
    }
  }, [
    organization,
    filters.search,
    filters.gender,
    filters.ageRange,
    filters.hasUpcomingAppointments,
    filters.department,
    filters.dateRange,
    shouldFilterByLocation,
    selectedLocation,
  ]);

  useEffect(() => {
    fetchPatients();
  }, [fetchPatients]);

  const refetch = useCallback(() => {
    fetchPatients();
  }, [fetchPatients]);

  return {
    patients,
    isLoading,
    error,
    totalCount,
    refetch,
    // Context information for UI
    isLocationFiltered: shouldFilterByLocation,
    currentLocation: selectedLocation,
  };
}
