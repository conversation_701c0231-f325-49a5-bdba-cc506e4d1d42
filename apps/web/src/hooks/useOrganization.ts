import { isUserSystemAdmin } from "@/services/organization-service";
import { useOrganizationStore } from "@/stores/organization-store";
import { useEffect, useState } from "react";
import { useAuth } from "./useAuth";

export function useOrganization() {
  const { user } = useAuth();
  const [isSystemAdmin, setIsSystemAdmin] = useState(false);
  const {
    currentOrg,
    availableOrgs,
    isLoading,
    error,
    loadUserOrganizations,
    switchOrganization,
    clearCache
  } = useOrganizationStore();

  // Check if user is system admin
  useEffect(() => {
    if (user) {
      isUserSystemAdmin(user.id).then(setIsSystemAdmin);
    }
  }, [user]);

  // Auto-load organizations when user changes
  useEffect(() => {
    if (user && !currentOrg && !isLoading) {
      loadUserOrganizations(user);
    }
  }, [user, currentOrg, isLoading, loadUserOrganizations]);

  // Clear cache when user signs out
  useEffect(() => {
    if (!user) {
      clearCache();
    }
  }, [user, clearCache]);

  return {
    currentOrg,
    availableOrgs,
    isLoading,
    error,
    switchOrganization,
    hasMultipleOrgs: availableOrgs.length > 1,
    isSystemAdmin
  };
} 