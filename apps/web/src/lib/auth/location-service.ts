import { supabase } from "@/lib/supabase";
import {
    ALL_LOCATIONS_ID,
    AllLocationsLocation,
    Location,
} from "@/types/location";
import type { Session, User } from "@supabase/supabase-js";

// Cache for location data to avoid repeated requests
const locationCache = new Map<string, Promise<Location | null>>();

// Special "All Locations" location for system admins and multi-location users
const createAllLocationsLocation = (
  organizationId: string,
): AllLocationsLocation => ({
  id: ALL_LOCATIONS_ID,
  organization_id: organizationId,
  name: "All Locations",
  type: "system",
  address: {},
  contact_info: {},
  operating_hours: null,
  settings: {},
  created_at: new Date().toISOString(),
  updated_at: new Date().toISOString(),
});

/**
 * Fetch locations for a user within a specific organization
 */
export async function fetchUserLocations(
  user: User,
  _session: Session,
  organizationId: string,
): Promise<Location[]> {
  try {
    // First check if user is a system admin (they have organization_id = NULL)
    const { data: systemAdminRoles, error: systemAdminError } = await supabase
      .from("user_roles")
      .select("role")
      .eq("user_id", user.id)
      .is("organization_id", null)
      .eq("role", "system_admin");

    if (systemAdminError) {
      console.error("Error checking system admin roles:", systemAdminError);
      return [];
    }

    if (systemAdminRoles && systemAdminRoles.length > 0) {

      // Check if this is the "All Organizations" view
      if (organizationId === "system-admin-all-orgs") {
        // System admin viewing "All Organizations" - get all locations from all orgs
        const { data: allLocations, error: locationsError } = await supabase
          .from("locations")
          .select("*, organizations(name)")
          .order("name");

        if (locationsError) {
          console.error("Error fetching all locations across organizations:", locationsError);
          return [];
        }

        return allLocations || [];
      } else {
        // System admin viewing a specific organization
        const { data: allLocations, error: locationsError } = await supabase
          .from("locations")
          .select("*")
          .eq("organization_id", organizationId)
          .order("name");

        if (locationsError) {
          console.error("Error fetching locations for organization:", locationsError);
          return [];
        }

        return allLocations || [];
      }
    }

    // Check user roles for specific organization to determine location access
    const { data: userRoles, error: rolesError } = await supabase
      .from("user_roles")
      .select(
        `
        role,
        department_id,
        departments!inner(
          location_id,
          locations!inner(*)
        )
      `,
      )
      .eq("user_id", user.id)
      .eq("organization_id", organizationId);

    if (rolesError) {
      console.error("Error fetching user roles for locations:", rolesError);
      return [];
    }

    // For non-system admins, get locations based on their department assignments
    const userLocations = new Map<string, Location>();

    userRoles?.forEach((role) => {
      if (role.departments?.locations) {
        const location = role.departments.locations;
        userLocations.set(location.id, location);
      }
    });

    return Array.from(userLocations.values()).sort((a, b) =>
      a.name.localeCompare(b.name),
    );
  } catch (error) {
    console.error("Error in fetchUserLocations:", error);
    return [];
  }
}

/**
 * Fetch a specific location by ID (with permission check)
 */
export async function fetchLocationById(
  locationId: string,
  user: User,
  organizationId?: string,
): Promise<Location | null> {
  const cacheKey = `${user.id}-${locationId}`;

  if (locationCache.has(cacheKey)) {
    return locationCache.get(cacheKey)!;
  }

  const locationPromise = (async () => {
    try {
      // Special case for "All Locations"
      if (locationId === ALL_LOCATIONS_ID && organizationId) {
        return createAllLocationsLocation(organizationId) as Location;
      }

      // Fetch the location
      const { data: location, error } = await supabase
        .from("locations")
        .select("*")
        .eq("id", locationId)
        .single();

      if (error || !location) {
        console.error("Error fetching location:", error);
        return null;
      }

      // Check if user has access to this location
      const hasAccess = await checkLocationAccess(user.id, locationId);

      if (!hasAccess) {
        console.warn(
          `User ${user.id} does not have access to location ${locationId}`,
        );
        return null;
      }

      return location;
    } catch (error) {
      console.error("Error in fetchLocationById:", error);
      return null;
    }
  })();

  locationCache.set(cacheKey, locationPromise);
  return locationPromise;
}

/**
 * Check if a user has access to a specific location
 */
async function checkLocationAccess(
  userId: string,
  locationId: string,
): Promise<boolean> {
  try {
    // Check if user is system admin (has access to all locations)
    const { data: systemAdminRole } = await supabase
      .from("user_roles")
      .select("role")
      .eq("user_id", userId)
      .eq("role", "system_admin")
      .limit(1);

    if (systemAdminRole && systemAdminRole.length > 0) {
      return true;
    }

    // Check if user has a role in a department within this location
    const { data: userAccess } = await supabase
      .from("user_roles")
      .select(
        `
        departments!inner(
          location_id
        )
      `,
      )
      .eq("user_id", userId)
      .eq("departments.location_id", locationId)
      .limit(1);

    return !!(userAccess && userAccess.length > 0);
  } catch (error) {
    console.error("Error checking location access:", error);
    return false;
  }
}

/**
 * Get the default location for a user within an organization
 */
export async function getDefaultLocation(
  user: User,
  session: Session,
  organizationId: string,
): Promise<Location | null> {
  try {
    const locations = await fetchUserLocations(user, session, organizationId);

    if (locations.length === 0) {
      return null;
    }

    // If user has access to multiple locations, return "All Locations"
    if (locations.length > 1) {
      return createAllLocationsLocation(organizationId) as Location;
    }

    // If user has access to only one location, return that location
    return locations[0];
  } catch (error) {
    console.error("Error getting default location:", error);
    return null;
  }
}

/**
 * Clear location cache for a user
 */
export function clearLocationCache(userId: string): void {
  const keysToDelete = Array.from(locationCache.keys()).filter((key) =>
    key.startsWith(`${userId}-`),
  );

  keysToDelete.forEach((key) => locationCache.delete(key));
}
