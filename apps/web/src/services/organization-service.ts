import { supabase } from "@/lib/supabase";
import type { Database } from "@spritely/supabase-types";
import { User } from "@supabase/supabase-js";

type Organization = Database["public"]["Tables"]["organizations"]["Row"];

export async function getUserOrganizations(user: User): Promise<Organization[]> {
  const { data: userRoles } = await supabase
    .from("user_roles")
    .select("organization_id, role")
    .eq("user_id", user.id);

  if (!userRoles?.length) return [];

  const isSystemAdmin = userRoles.some(role => role.role === "system_admin");

  if (isSystemAdmin) {
    // System admin gets all organizations with a special "All Organizations" entry at the top
    const { data } = await supabase
      .from("organizations")
      .select("*")
      .order("name");

    const allOrgs = data || [];

    // Add a virtual "All Organizations" entry for system admins - this should be the DEFAULT
    const allOrganizationsEntry: Organization = {
      id: "system-admin-all-orgs",
      name: "All Organizations",
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
      description: "System Administrator view of all organizations",
      address: null,
      phone: null,
      email: null,
      website: null,
      logo_url: null,
      settings: null,
      timezone: "UTC",
      status: "active" as const
    };

    return [allOrganizationsEntry, ...allOrgs];
  }

  // Regular user gets their organizations
  const orgIds = userRoles.map(role => role.organization_id).filter(Boolean);
  if (!orgIds.length) return [];

  const { data } = await supabase
    .from("organizations")
    .select("*")
    .in("id", orgIds)
    .order("name");

  return data || [];
}

export async function getOrganizationHierarchy(orgId: string): Promise<Organization[]> {
  // Future: implement hierarchical organization support for FHIR compliance
  // For now, return empty array since parent_id column doesn't exist yet
  return [];
}

export async function isUserSystemAdmin(userId: string): Promise<boolean> {
  const { data } = await supabase
    .from("user_roles")
    .select("role")
    .eq("user_id", userId)
    .eq("role", "system_admin")
    .single();

  return !!data;
}